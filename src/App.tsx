import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './context/AuthContext';
import { ChannelProvider } from './context/ChannelContext';
import LoginPage from './components/Auth/LoginPage';
import MainLayout from './components/Layout/MainLayout';
import NewsSourceManager from './components/NewsSourceManagement/NewsSourceManager';
import ChannelManager from './components/ChannelManagement/ChannelManager';
import ArticleLinksManager from './components/NewsSourceManagement/ArticleLinksManager';
import NewsContentManager from './components/NewsContentManagement/NewsContentManager';
import LogMonitorPage from './components/LogMonitor/LogMonitorPage';
import { ROUTES, DEFAULT_ROUTE } from './config/routes';

const PrivateRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth();
  
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
      </div>
    );
  }
  
  return user ? <>{children}</> : <Navigate to="/login" />;
};

const App = () => {
  return (
    <AuthProvider>
      <ChannelProvider>
        <BrowserRouter>
          <Routes>
            <Route path={ROUTES.LOGIN} element={<LoginPage />} />
            <Route
              path="/"
              element={
                <PrivateRoute>
                  <MainLayout />
                </PrivateRoute>
              }
            >
              <Route index element={<Navigate to={DEFAULT_ROUTE} replace />} />
              <Route path={ROUTES.CHANNELS.slice(1)} element={<ChannelManager />} />
              <Route path={ROUTES.NEWS_SOURCES.slice(1)} element={<NewsSourceManager />} />
              <Route path={ROUTES.ARTICLE_LINKS.slice(1)} element={<ArticleLinksManager />} />
              <Route path={ROUTES.ARTICLE_CONTENT.slice(1)} element={<NewsContentManager />} />
              <Route path={ROUTES.FUNCTIONS.slice(1)} element={<div>Functions</div>} />
              <Route path={ROUTES.ACTIVITY_LOG.slice(1)} element={<LogMonitorPage />} />
            </Route>
          </Routes>
        </BrowserRouter>
      </ChannelProvider>
    </AuthProvider>
  );
};

export default App;