import React from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './context/AuthContext';
import { ChannelProvider } from './context/ChannelContext';
import LoginPage from './components/Auth/LoginPage';
import MainLayout from './components/Layout/MainLayout';
import NewsSourceManager from './components/NewsSourceManagement/NewsSourceManager';
import ChannelManager from './components/ChannelManagement/ChannelManager';
import ArticleLinksManager from './components/NewsSourceManagement/ArticleLinksManager';
import NewsContentManager from './components/NewsContentManagement/NewsContentManager';
import LogMonitorPage from './components/LogMonitor/LogMonitorPage';

const PrivateRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth();
  
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
      </div>
    );
  }
  
  return user ? <>{children}</> : <Navigate to="/login" />;
};

const App = () => {
  return (
    <AuthProvider>
      <ChannelProvider>
        <BrowserRouter>
          <Routes>
            <Route path="/login" element={<LoginPage />} />
            <Route
              path="/"
              element={
                <PrivateRoute>
                  <MainLayout />
                </PrivateRoute>
              }
            >
              <Route index element={<Navigate to="/dashboard/channels" replace />} />
              <Route path="dashboard">
                <Route path="channels" element={<ChannelManager />} />
                <Route path="sources" element={<NewsSourceManager />} />
                <Route path="article-links" element={<ArticleLinksManager />} />
                <Route path="content" element={<NewsContentManager />} />
                <Route path="functions" element={<div>Functions</div>} />
                <Route path="activity" element={<LogMonitorPage />} />
              </Route>
            </Route>
          </Routes>
        </BrowserRouter>
      </ChannelProvider>
    </AuthProvider>
  );
};

export default App;