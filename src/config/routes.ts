import { 
  Globe, 
  Newspaper, 
  Link, 
  Database, 
  Terminal, 
  Activity 
} from 'lucide-react';

// Centralized route configuration
export const ROUTES = {
  // Auth routes
  LOGIN: '/login',
  
  // Main app routes
  HOME: '/',
  CHANNELS: '/channels',
  NEWS_SOURCES: '/news-sources',
  ARTICLE_LINKS: '/article-links',
  ARTICLE_CONTENT: '/article-content',
  FUNCTIONS: '/functions',
  ACTIVITY_LOG: '/activity-log',
} as const;

// Navigation menu configuration
export const NAVIGATION_ITEMS = [
  { 
    icon: Globe, 
    label: 'Channels', 
    path: ROUTES.CHANNELS 
  },
  { 
    icon: Newspaper, 
    label: 'News Sources', 
    path: ROUTES.NEWS_SOURCES 
  },
  { 
    icon: Link, 
    label: 'Article Links', 
    path: ROUTES.ARTICLE_LINKS 
  },
  { 
    icon: Database, 
    label: 'Article Content', 
    path: ROUTES.ARTICLE_CONTENT 
  },
  { 
    icon: Terminal, 
    label: 'Functions', 
    path: ROUTES.FUNCTIONS 
  },
  { 
    icon: Activity, 
    label: 'Activity Log', 
    path: ROUTES.ACTIVITY_LOG 
  },
] as const;

// Default redirect route
export const DEFAULT_ROUTE = ROUTES.CHANNELS;
