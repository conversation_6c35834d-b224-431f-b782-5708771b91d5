import React, { useState } from 'react';
import { ref, get } from 'firebase/database';
import { httpsCallable } from 'firebase/functions';
import { database, functions } from '../../firebaseConfig';
import { X, RefreshCw, Download, ExternalLink, Loader2 } from 'lucide-react';

interface ExtractArticlesModalProps {
  isOpen: boolean;
  onClose: () => void;
  channelId: string;
  onSuccess?: () => void;
}

const ExtractArticlesModal: React.FC<ExtractArticlesModalProps> = ({
  isOpen,
  onClose,
  channelId,
  onSuccess
}) => {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [articleLinks, setArticleLinks] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [extracting, setExtracting] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [messageType, setMessageType] = useState<'success' | 'error' | 'info'>('info');

  // Get the callable function reference
  const extractArticles = httpsCallable(functions, 'callable_extract_article_content', {
    timeout: 540000 // 9 minute timeout
  });

  const fetchLinks = async () => {
    if (!channelId || !selectedDate) {
      setMessage('Please select a valid date');
      setMessageType('error');
      return;
    }

    setLoading(true);
    setMessage(null);
    setArticleLinks([]);

    try {
      const [year, month, day] = selectedDate.split('-');
      const linksRef = ref(database, `${channelId}/article_links/${year}/${month}/${day}`);
      const snapshot = await get(linksRef);
      
      if (snapshot.exists()) {
        const links = snapshot.val() || [];
        if (Array.isArray(links) && links.length > 0) {
          setArticleLinks(links);
          setMessage(`Found ${links.length} article links for ${selectedDate}`);
          setMessageType('success');
        } else {
          setArticleLinks([]);
          setMessage('No article links found for this date.');
          setMessageType('info');
        }
      } else {
        setArticleLinks([]);
        setMessage('No article links found for this date.');
        setMessageType('info');
      }
    } catch (error) {
      console.error('Error fetching links:', error);
      setMessage('Failed to fetch article links. Please try again.');
      setMessageType('error');
      setArticleLinks([]);
    } finally {
      setLoading(false);
    }
  };

  const startExtraction = async () => {
    if (!channelId || articleLinks.length === 0) {
      setMessage('No article links available for extraction');
      setMessageType('error');
      return;
    }

    setExtracting(true);
    setMessage('Starting article extraction... This may take up to 9 minutes.');
    setMessageType('info');

    try {
      console.log('Calling extraction function with:', {
        channel: channelId,
        article_links: articleLinks
      });

      const result = await extractArticles({
        channel: channelId,
        article_links: articleLinks
      });

      console.log('Extraction result:', result.data);

      const response = result.data as any;
      
      if (response && response.status === 'success') {
        setMessage(`Successfully extracted ${response.extracted_success || 'some'} articles! ${response.message || ''}`);
        setMessageType('success');
        // Call onSuccess callback to refresh the articles list
        if (onSuccess) {
          onSuccess();
        }
      } else if (response && response.status === 'error') {
        setMessage(`Extraction failed: ${response.message || 'Unknown error'}`);
        setMessageType('error');
      } else {
        setMessage('Unknown Response. Check the results in the content management section.');
        setMessageType('error');
        // Call onSuccess callback even for unknown response (might still have extracted some)
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (error: any) {
      console.error('Error calling extraction function:', error);
      let errorMessage = 'Failed to extract articles. ';
      
      if (error.code === 'functions/timeout') {
        errorMessage += 'The operation timed out. Some articles may have been processed.';
      } else if (error.message) {
        errorMessage += error.message;
      } else {
        errorMessage += 'Please try again.';
      }
      
      setMessage(errorMessage);
      setMessageType('error');
    } finally {
      setExtracting(false);
    }
  };

  const handleClose = () => {
    if (!loading && !extracting) {
      setSelectedDate(new Date().toISOString().split('T')[0]);
      setArticleLinks([]);
      setMessage(null);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">Extract Articles</h2>
          <button
            onClick={handleClose}
            disabled={loading || extracting}
            className="text-gray-400 hover:text-white disabled:opacity-50"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Date Picker Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Select Date to Fetch Article Links
                </label>
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  disabled={loading || extracting}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50"
                />
              </div>
              <button
                onClick={fetchLinks}
                disabled={loading || extracting || !selectedDate}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed mt-6"
              >
                <RefreshCw size={20} className={loading ? 'animate-spin' : ''} />
                {loading ? 'Fetching...' : 'Fetch Links'}
              </button>
            </div>
          </div>

          {/* Message Display */}
          {message && (
            <div className={`p-4 rounded-md ${
              messageType === 'success' ? 'bg-green-500/10 border border-green-500 text-green-500' :
              messageType === 'error' ? 'bg-red-500/10 border border-red-500 text-red-500' :
              'bg-blue-500/10 border border-blue-500 text-blue-500'
            }`}>
              {message}
            </div>
          )}

          {/* Article Links Display Area */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">
              Article Links ({articleLinks.length} found)
            </label>
            <div className="bg-gray-700 border border-gray-600 rounded-md p-4 h-64 overflow-y-auto">
              {articleLinks.length > 0 ? (
                <div className="space-y-2">
                  {articleLinks.map((link, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 bg-gray-600 rounded text-sm">
                      <span className="flex-1 text-white truncate">{link}</span>
                      <a
                        href={link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-400 hover:text-blue-300"
                      >
                        <ExternalLink size={14} />
                      </a>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="h-full flex items-center justify-center text-gray-400">
                  {loading ? 'Loading article links...' : 'No article links to display. Select a date and click "Fetch Links".'}
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-700">
            <button
              onClick={handleClose}
              disabled={loading || extracting}
              className="px-4 py-2 text-gray-300 hover:text-white transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={startExtraction}
              disabled={loading || extracting || articleLinks.length === 0}
              className="flex items-center gap-2 px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {extracting ? (
                <Loader2 size={20} className="animate-spin" />
              ) : (
                <Download size={20} />
              )}
              {extracting ? 'Extracting...' : 'Start Extraction'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExtractArticlesModal;
