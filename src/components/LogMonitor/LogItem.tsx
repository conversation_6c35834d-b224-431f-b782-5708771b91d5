import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Clock, AlertCircle, Info, AlertTriangle } from 'lucide-react';
import { format } from 'date-fns';

interface LogEntry {
  id: string;
  severity: 'INFO' | 'ERROR' | 'WARNING';
  message: string;
  timestamp: number;
  log_id: string;
  data?: any;
}

interface LogItemProps {
  log: LogEntry;
}

const LogItem: React.FC<LogItemProps> = ({ log }) => {
  const [showData, setShowData] = useState(false);

  // Format timestamp to human-readable date
  const formatTimestamp = (timestamp: number): string => {
    try {
      // Extract timestamp from log ID if timestamp field is not available
      const ts = timestamp || parseInt(log.id.split('-')[0]);
      return format(new Date(ts), 'MMM dd, yyyy, h:mm:ss a');
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Get severity badge styling
  const getSeverityBadge = (severity: string) => {
    const baseClasses = "inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium";
    
    switch (severity) {
      case 'ERROR':
        return {
          className: `${baseClasses} bg-red-500/20 text-red-400 border border-red-500/30`,
          icon: <AlertCircle size={12} />
        };
      case 'WARNING':
        return {
          className: `${baseClasses} bg-yellow-500/20 text-yellow-400 border border-yellow-500/30`,
          icon: <AlertTriangle size={12} />
        };
      case 'INFO':
      default:
        return {
          className: `${baseClasses} bg-blue-500/20 text-blue-400 border border-blue-500/30`,
          icon: <Info size={12} />
        };
    }
  };

  const severityBadge = getSeverityBadge(log.severity);

  // Format JSON data for display
  const formatJsonData = (data: any): string => {
    try {
      return JSON.stringify(data, null, 2);
    } catch (error) {
      return 'Invalid JSON data';
    }
  };

  return (
    <div className="bg-gray-700 border border-gray-600 rounded-lg p-4 hover:bg-gray-600/50 transition-colors">
      {/* Header Row */}
      <div className="flex items-start justify-between gap-4 mb-2">
        <div className="flex items-center gap-3">
          {/* Severity Badge */}
          <span className={severityBadge.className}>
            {severityBadge.icon}
            {log.severity}
          </span>
          
          {/* Log ID */}
          <span className="text-xs text-gray-400 font-mono">
            {log.log_id || log.id}
          </span>
        </div>
        
        {/* Timestamp */}
        <div className="flex items-center gap-1 text-xs text-gray-400">
          <Clock size={12} />
          {formatTimestamp(log.timestamp)}
        </div>
      </div>

      {/* Message */}
      <div className="mb-3">
        <p className="text-white text-sm leading-relaxed">
          {log.message}
        </p>
      </div>

      {/* Data Section (if exists) */}
      {log.data && (
        <div className="border-t border-gray-600 pt-3">
          <button
            onClick={() => setShowData(!showData)}
            className="flex items-center gap-2 text-sm text-indigo-400 hover:text-indigo-300 transition-colors mb-2"
          >
            {showData ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
            {showData ? 'Hide Data' : 'View Data'}
            <span className="text-xs text-gray-400">
              ({Object.keys(log.data).length} field{Object.keys(log.data).length !== 1 ? 's' : ''})
            </span>
          </button>
          
          {showData && (
            <div className="bg-gray-800 border border-gray-600 rounded-md p-3 overflow-x-auto">
              <pre className="text-xs text-gray-300 whitespace-pre-wrap font-mono">
                {formatJsonData(log.data)}
              </pre>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default LogItem;
