import React, { useState, useEffect, useCallback, useRef } from 'react';
import { ref, query, orderByKey, limitToLast, endAt, get } from 'firebase/database';
import { database } from '../../firebaseConfig';
import { Calendar, Filter, RefreshCw, Plus } from 'lucide-react';
import LogItem from './LogItem';
import { generateTestLogs } from '../../utils/testLogs';

interface LogEntry {
  id: string;
  severity: 'INFO' | 'ERROR' | 'WARNING';
  message: string;
  timestamp: number;
  log_id: string;
  data?: any;
}

const LogMonitorPage: React.FC = () => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filter states
  const [startDate, setStartDate] = useState(() => {
    const date = new Date();
    date.setDate(date.getDate() - 7); // Default to last 7 days
    return date.toISOString().split('T')[0];
  });
  const [endDate, setEndDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedSeverities, setSelectedSeverities] = useState<string[]>(['INFO', 'ERROR', 'WARNING']);
  
  // Pagination
  const [oldestKey, setOldestKey] = useState<string | null>(null);
  const observer = useRef<IntersectionObserver>();
  
  const severityOptions = ['INFO', 'ERROR', 'WARNING'];

  // Convert date to timestamp key format
  const dateToTimestamp = (dateString: string, isEndDate = false): string => {
    const date = new Date(dateString);
    if (isEndDate) {
      date.setHours(23, 59, 59, 999); // End of day
    } else {
      date.setHours(0, 0, 0, 0); // Start of day
    }
    return date.getTime().toString();
  };

  // Fetch logs from Firebase
  const fetchLogs = useCallback(async (isLoadMore = false) => {
    if (loading) return;
    
    setLoading(true);
    setError(null);

    try {
      const startTimestamp = dateToTimestamp(startDate);
      const endTimestamp = dateToTimestamp(endDate, true);
      
      let logsQuery = query(
        ref(database, 'logs'),
        orderByKey(),
        limitToLast(20)
      );

      // Add date range filtering
      if (startTimestamp && endTimestamp) {
        logsQuery = query(
          ref(database, 'logs'),
          orderByKey(),
          ...(isLoadMore && oldestKey ? [endAt(oldestKey)] : []),
          limitToLast(isLoadMore ? 21 : 20) // +1 for pagination overlap
        );
      }

      const snapshot = await get(logsQuery);
      
      if (snapshot.exists()) {
        const data = snapshot.val();
        let fetchedLogs: LogEntry[] = Object.entries(data).map(([key, value]: [string, any]) => ({
          id: key,
          ...value
        }));

        // Filter by date range (client-side for precision)
        fetchedLogs = fetchedLogs.filter(log => {
          const logTimestamp = parseInt(log.id.split('-')[0]);
          const startTs = parseInt(startTimestamp);
          const endTs = parseInt(endTimestamp);
          return logTimestamp >= startTs && logTimestamp <= endTs;
        });

        // Filter by severity (client-side)
        fetchedLogs = fetchedLogs.filter(log => 
          selectedSeverities.includes(log.severity)
        );

        // Sort by timestamp (newest first)
        fetchedLogs.sort((a, b) => {
          const aTime = parseInt(a.id.split('-')[0]);
          const bTime = parseInt(b.id.split('-')[0]);
          return bTime - aTime;
        });

        if (isLoadMore && fetchedLogs.length > 0) {
          // Remove first item to avoid duplication
          fetchedLogs = fetchedLogs.slice(1);
          setLogs(prev => [...prev, ...fetchedLogs]);
        } else {
          setLogs(fetchedLogs);
        }

        // Update pagination state
        if (fetchedLogs.length > 0) {
          const oldestLog = fetchedLogs[fetchedLogs.length - 1];
          setOldestKey(oldestLog.id);
        }

        setHasMore(fetchedLogs.length === (isLoadMore ? 20 : 20));
      } else {
        if (!isLoadMore) {
          setLogs([]);
        }
        setHasMore(false);
      }
    } catch (err) {
      console.error('Error fetching logs:', err);
      setError('Failed to fetch logs. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [startDate, endDate, selectedSeverities, oldestKey, loading]);

  // Handle severity filter toggle
  const toggleSeverity = (severity: string) => {
    setSelectedSeverities(prev => 
      prev.includes(severity)
        ? prev.filter(s => s !== severity)
        : [...prev, severity]
    );
  };

  // Reset and fetch logs when filters change
  useEffect(() => {
    setLogs([]);
    setOldestKey(null);
    setHasMore(true);
    fetchLogs();
  }, [startDate, endDate, selectedSeverities]);

  // Infinite scroll observer
  const lastLogRef = useCallback((node: HTMLDivElement) => {
    if (loading) return;
    if (observer.current) observer.current.disconnect();
    
    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        fetchLogs(true);
      }
    });
    
    if (node) observer.current.observe(node);
  }, [loading, hasMore, fetchLogs]);

  return (
    <div className="flex flex-col h-[calc(100vh-theme(spacing.14)-theme(spacing.8))] md:h-[calc(100vh-theme(spacing.12))]">
      {/* Header */}
      <div className="flex-none mb-6">
        <h1 className="text-2xl font-bold text-white mb-6">Activity Log Monitor</h1>
        
        {/* Filter Bar */}
        <div className="bg-gray-800 p-4 rounded-lg border border-gray-700 space-y-4">
          <div className="flex flex-wrap items-center gap-4">
            {/* Date Range Filters */}
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-300">Start Date:</label>
              <div className="relative">
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="bg-gray-700 text-white px-3 py-2 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
                <Calendar className="absolute right-3 top-2.5 text-gray-400 pointer-events-none" size={16} />
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-300">End Date:</label>
              <div className="relative">
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="bg-gray-700 text-white px-3 py-2 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
                <Calendar className="absolute right-3 top-2.5 text-gray-400 pointer-events-none" size={16} />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <button
                onClick={async () => {
                  await generateTestLogs();
                  setLogs([]);
                  setOldestKey(null);
                  setHasMore(true);
                  fetchLogs();
                }}
                disabled={loading}
                className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
              >
                <Plus size={16} />
                Add Test Logs
              </button>

              <button
                onClick={() => {
                  setLogs([]);
                  setOldestKey(null);
                  setHasMore(true);
                  fetchLogs();
                }}
                disabled={loading}
                className="flex items-center gap-2 px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors disabled:opacity-50"
              >
                <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
                Refresh
              </button>
            </div>
          </div>

          {/* Severity Filters */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter size={16} className="text-gray-400" />
              <span className="text-sm font-medium text-gray-300">Severity:</span>
            </div>
            <div className="flex gap-2">
              {severityOptions.map(severity => (
                <button
                  key={severity}
                  onClick={() => toggleSeverity(severity)}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    selectedSeverities.includes(severity)
                      ? severity === 'ERROR' 
                        ? 'bg-red-600 text-white' 
                        : severity === 'WARNING'
                        ? 'bg-yellow-600 text-white'
                        : 'bg-blue-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  {severity}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="flex-none bg-red-500/10 border border-red-500 text-red-500 p-4 rounded-md mb-4">
          {error}
        </div>
      )}

      {/* Logs Display */}
      <div className="flex-1 bg-gray-800 rounded-lg overflow-hidden">
        {logs.length > 0 ? (
          <div className="h-full overflow-y-auto p-4 space-y-3">
            {logs.map((log, index) => (
              <div
                key={log.id}
                ref={index === logs.length - 1 ? lastLogRef : undefined}
              >
                <LogItem log={log} />
              </div>
            ))}
            
            {loading && (
              <div className="flex justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
              </div>
            )}
          </div>
        ) : loading ? (
          <div className="h-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
          </div>
        ) : (
          <div className="h-full flex items-center justify-center text-gray-400">
            No logs found for the selected criteria
          </div>
        )}
      </div>
    </div>
  );
};

export default LogMonitorPage;
