import React, { useState } from 'react';
import { doc, setDoc, deleteDoc } from 'firebase/firestore';
import { firestore } from '../../firebaseConfig';
import { useChannels } from '../../context/ChannelContext';
import { Plus, Edit2, Trash2, X } from 'lucide-react';

interface ChannelFormData {
  id: string;
  name: string;
  type: string;
}

const initialFormState: ChannelFormData = {
  id: '',
  name: '',
  type: 'country',
};

const channelTypes = [
  { value: 'country', label: 'Country' },
  { value: 'region', label: 'Region' },
  { value: 'technology', label: 'Technology' },
  { value: 'industry', label: 'Industry' },
  { value: 'topic', label: 'Topic' },
  { value: 'ai', label: 'Artificial Intelligence' },
  { value: 'ev', label: 'Electric Vehicles' },
  { value: 'crypto', label: 'Cryptocurrency' },
  { value: 'space', label: 'Space Technology' },
  { value: 'gaming', label: 'Gaming' },
  { value: 'cybersecurity', label: 'Cybersecurity' },
  { value: 'biotech', label: 'Biotechnology' },
  { value: 'fintech', label: 'Financial Technology' },
];

const ChannelManager = () => {
  const { channels, loading, error: contextError, refreshChannels } = useChannels();
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState<ChannelFormData>(initialFormState);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [editMode, setEditMode] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const validateForm = () => {
    if (!formData.id.trim() || !formData.name.trim()) {
      setError('All fields are required');
      return false;
    }
    if (!/^[A-Z]{2,3}$/.test(formData.id)) {
      setError('Channel ID must be 2-3 uppercase letters');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setSaving(true);
    try {
      const channelRef = doc(firestore, 'channels', formData.id);
      await setDoc(channelRef, {
        id: formData.id,
        name: formData.name,
        type: formData.type,
      });
      
      await refreshChannels();
      resetForm();
      setError(null);
    } catch (err) {
      setError('Failed to save channel');
      console.error(err);
    }
    setSaving(false);
  };

  const handleEdit = (channel: { id: string; name: string; type: string }) => {
    setFormData(channel);
    setEditMode(true);
    setShowForm(true);
    setError(null);
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this channel?')) return;

    try {
      await deleteDoc(doc(firestore, 'channels', id));
      await refreshChannels();
      setError(null);
    } catch (err) {
      setError('Failed to delete channel');
      console.error(err);
    }
  };

  const resetForm = () => {
    setFormData(initialFormState);
    setShowForm(false);
    setEditMode(false);
    setError(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl font-bold text-white">Channel Management</h1>
        {!showForm && (
          <button 
            onClick={() => setShowForm(true)}
            className="w-full sm:w-auto flex items-center justify-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
          >
            <Plus size={20} />
            Add Channel
          </button>
        )}
      </div>

      {(error || contextError) && (
        <div className="bg-red-500/10 border border-red-500 text-red-500 p-4 rounded-md">
          {error || contextError}
        </div>
      )}

      {showForm && (
        <div className="bg-gray-800 p-4 sm:p-6 rounded-lg border border-gray-700">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-white">
              {editMode ? 'Edit Channel' : 'Add New Channel'}
            </h2>
            <button 
              onClick={resetForm}
              className="text-gray-400 hover:text-white"
            >
              <X size={20} />
            </button>
          </div>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Channel ID
              </label>
              <input
                type="text"
                name="id"
                value={formData.id}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="US"
                maxLength={3}
                readOnly={editMode}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Channel Name
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="United States"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Type
              </label>
              <select
                name="type"
                value={formData.type}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                {channelTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex flex-col sm:flex-row justify-end gap-3">
              <button
                type="button"
                onClick={resetForm}
                className="w-full sm:w-auto px-4 py-2 text-gray-300 hover:text-white transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={saving}
                className="w-full sm:w-auto px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? 'Saving...' : editMode ? 'Update Channel' : 'Add Channel'}
              </button>
            </div>
          </form>
        </div>
      )}

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
        </div>
      ) : (
        <div className="bg-gray-800 rounded-lg overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-700">
                <th className="px-6 py-3 text-left text-sm font-medium text-gray-300">ID</th>
                <th className="px-6 py-3 text-left text-sm font-medium text-gray-300">Name</th>
                <th className="px-6 py-3 text-left text-sm font-medium text-gray-300">Type</th>
                <th className="px-6 py-3 text-right text-sm font-medium text-gray-300">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {channels.map((channel) => (
                <tr key={channel.id} className="hover:bg-gray-700/50">
                  <td className="px-6 py-4 text-sm text-white">{channel.id}</td>
                  <td className="px-6 py-4 text-sm text-white">{channel.name}</td>
                  <td className="px-6 py-4 text-sm text-white capitalize">
                    {channelTypes.find(type => type.value === channel.type)?.label || channel.type}
                  </td>
                  <td className="px-6 py-4 text-sm text-right space-x-2">
                    <button 
                      onClick={() => handleEdit(channel)}
                      className="text-blue-400 hover:text-blue-300"
                      title="Edit"
                    >
                      <Edit2 size={16} />
                    </button>
                    <button 
                      onClick={() => handleDelete(channel.id)}
                      className="text-red-400 hover:text-red-300"
                      title="Delete"
                    >
                      <Trash2 size={16} />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default ChannelManager;