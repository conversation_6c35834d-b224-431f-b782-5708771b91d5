import React, { useState, useEffect, useCallback } from 'react';
import { ref, get, set } from 'firebase/database';
import { database, functions } from '../../firebaseConfig';
import { Plus, Trash2, ExternalLink, RefreshCw, CheckSquare, Square } from 'lucide-react';
import { httpsCallable } from 'firebase/functions';
import { useChannels } from '../../context/ChannelContext';

interface NewsSource {
  source_name: string;
  news_page: string;
  rss_feed: string;
}

const ArticleLinksManager = () => {
  const { channels, loading: channelsLoading } = useChannels();
  const [selectedChannel, setSelectedChannel] = useState(() => {
    const saved = localStorage.getItem('selectedNewsChannel');
    return saved || (channels.length > 0 ? channels[0].id : '');
  });
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [links, setLinks] = useState<string[]>([]);
  const [newLink, setNewLink] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [fetching, setFetching] = useState(false);

  // News source management state
  const [newsSources, setNewsSources] = useState<Record<string, NewsSource>>({});
  const [selectedNewsSources, setSelectedNewsSources] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [loadingNewsSources, setLoadingNewsSources] = useState(false);

  const fetchAndStoreNewsLinks = httpsCallable(functions, 'callable_fetch_and_store_news_links');

  // Fetch news sources for the selected channel
  const fetchNewsSources = useCallback(async () => {
    if (!selectedChannel) return;

    setLoadingNewsSources(true);
    try {
      const sourcesRef = ref(database, `${selectedChannel}/news_sources`);
      const snapshot = await get(sourcesRef);
      const sources = snapshot.val() || {};
      setNewsSources(sources);

      // Reset selections when sources change
      setSelectedNewsSources([]);
      setSelectAll(false);
    } catch (err) {
      console.error('Failed to fetch news sources:', err);
      setError('Failed to fetch news sources for this channel');
    }
    setLoadingNewsSources(false);
  }, [selectedChannel]);

  const fetchLinks = useCallback(async () => {
    if (!selectedChannel || !selectedDate) return;

    setLoading(true);
    setError(null);

    try {
      const [year, month, day] = selectedDate.split('-');
      const linksRef = ref(database, `${selectedChannel}/article_links/${year}/${month}/${day}`);
      const snapshot = await get(linksRef);
      setLinks(snapshot.val() || []);
    } catch (err) {
      setError('Failed to fetch article links');
      console.error(err);
    }

    setLoading(false);
  }, [selectedChannel, selectedDate]);

  const handleFetchLinks = async () => {
    if (!selectedChannel) {
      setError('Please select a channel first');
      return;
    }

    if (selectedNewsSources.length === 0) {
      setError('Please select at least one news source');
      return;
    }

    setFetching(true);
    setError(null);

    try {
      console.log("Attempting to call Firebase function with channel:", selectedChannel);
      console.log("Selected news sources IDs:", selectedNewsSources);

      // Convert selected source IDs to full source objects
      const selectedSourceObjects = selectedNewsSources.map(sourceId => newsSources[sourceId]).filter(Boolean);
      console.log("Selected news source objects:", selectedSourceObjects);

      if (selectedSourceObjects.length === 0) {
        throw new Error('No valid news sources found. Please refresh and try again.');
      }

      const result = await fetchAndStoreNewsLinks({
        channel: selectedChannel,
        news_sources: selectedSourceObjects
      });

      console.log("Function result:", result);
      await fetchLinks();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch news links';
      setError(errorMessage);
      console.error(err);
    } finally {
      setFetching(false);
    }
  };

  // News source selection handlers
  const handleNewsSourceToggle = (sourceId: string) => {
    setSelectedNewsSources(prev => {
      const newSelection = prev.includes(sourceId)
        ? prev.filter(id => id !== sourceId)
        : [...prev, sourceId];

      // Update selectAll state based on selection
      const totalSources = Object.keys(newsSources).length;
      setSelectAll(newSelection.length === totalSources);

      return newSelection;
    });
  };

  const handleSelectAllToggle = () => {
    if (selectAll) {
      setSelectedNewsSources([]);
      setSelectAll(false);
    } else {
      setSelectedNewsSources(Object.keys(newsSources));
      setSelectAll(true);
    }
  };

  useEffect(() => {
    if (selectedChannel && selectedDate) {
      fetchLinks();
    }
  }, [selectedChannel, selectedDate, fetchLinks]);

  useEffect(() => {
    if (selectedChannel) {
      localStorage.setItem('selectedNewsChannel', selectedChannel);
      fetchNewsSources();
    }
  }, [selectedChannel, fetchNewsSources]);

  useEffect(() => {
    if (!selectedChannel && channels.length > 0) {
      setSelectedChannel(channels[0].id);
    }
  }, [channels, selectedChannel]);

  const validateUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const handleAddLink = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newLink.trim() || !validateUrl(newLink)) {
      setError('Please enter a valid URL');
      return;
    }

    setSaving(true);
    try {
      const [year, month, day] = selectedDate.split('-');
      const linksRef = ref(database, `${selectedChannel}/article_links/${year}/${month}/${day}`);

      const updatedLinks = [...new Set([...links, newLink])];
      await set(linksRef, updatedLinks);

      setLinks(updatedLinks);
      setNewLink('');
      setError(null);
    } catch (err) {
      setError('Failed to add link');
      console.error(err);
    }
    setSaving(false);
  };

  const handleDeleteLink = async (linkToDelete: string) => {
    if (!window.confirm('Are you sure you want to delete this link?')) return;

    setLoading(true);
    try {
      const [year, month, day] = selectedDate.split('-');
      const linksRef = ref(database, `${selectedChannel}/article_links/${year}/${month}/${day}`);

      const updatedLinks = links.filter(link => link !== linkToDelete);
      setLinks(updatedLinks);

      await set(linksRef, updatedLinks.length ? updatedLinks : null);

      setError(null);
    } catch (err) {
      setError('Failed to delete link');
      console.error(err);
    }
    setLoading(false);
  };

  return (
    <div className="flex flex-col h-[calc(100vh-theme(spacing.14)-theme(spacing.8))] md:h-[calc(100vh-theme(spacing.12))]">
      <div className="flex-none space-y-6">
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl font-bold text-white">Article Links</h1>
            <span className="px-2 py-1 text-sm bg-gray-700 text-gray-300 rounded-md">
              {links.length} {links.length === 1 ? 'link' : 'links'}
            </span>
            <button
              onClick={handleFetchLinks}
              disabled={fetching || channelsLoading || !selectedChannel || selectedNewsSources.length === 0}
              className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title={selectedNewsSources.length === 0 ? 'Please select at least one news source' : ''}
            >
              <RefreshCw size={20} className={fetching ? 'animate-spin' : ''} />
              {fetching ? 'Fetching...' : 'Scrape Links'}
            </button>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div>
          <label className="text-gray-300">Select Channel:</label>
          <select
            value={selectedChannel}
            onChange={(e) => setSelectedChannel(e.target.value)}
            className="ml-2 bg-gray-700 text-white px-3 py-2 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            {channels.map((channel) => (
              <option key={channel.id} value={channel.id}>
                {channel.name} ({channel.id})
              </option>
            ))}
          </select>
        </div>
        <div className="flex-1">
          <input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="w-full bg-gray-700 text-white px-3 py-2 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>
      </div>

        {/* News Source Selection */}
        {selectedChannel && (
          <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-white">Select News Sources</h3>
              {loadingNewsSources ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              ) : (
                <span className="text-sm text-gray-400">
                  {selectedNewsSources.length} of {Object.keys(newsSources).length} selected
                </span>
              )}
            </div>

            {loadingNewsSources ? (
              <div className="text-center py-4 text-gray-400">Loading news sources...</div>
            ) : Object.keys(newsSources).length > 0 ? (
              <div className="space-y-3">
                {/* Select All Toggle */}
                <div className="flex items-center gap-3 p-2 bg-gray-700 rounded-md">
                  <button
                    onClick={handleSelectAllToggle}
                    className="flex items-center gap-2 text-white hover:text-indigo-300"
                  >
                    {selectAll ? <CheckSquare size={20} /> : <Square size={20} />}
                    <span className="font-medium">Select All</span>
                  </button>
                </div>

                {/* Individual News Sources */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 max-h-48 overflow-y-auto">
                  {Object.entries(newsSources).map(([sourceId, source]) => (
                    <div key={sourceId} className="flex items-center gap-3 p-2 bg-gray-700 rounded-md hover:bg-gray-600">
                      <button
                        onClick={() => handleNewsSourceToggle(sourceId)}
                        className="flex items-center gap-2 text-white hover:text-indigo-300 flex-1 text-left"
                      >
                        {selectedNewsSources.includes(sourceId) ?
                          <CheckSquare size={16} /> :
                          <Square size={16} />
                        }
                        <span className="truncate">{source.source_name}</span>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-4 text-gray-400">
                No news sources found for this channel. Please add some news sources first.
              </div>
            )}
          </div>
        )}

        {error && (
          <div className="bg-red-500/10 border border-red-500 text-red-500 p-4 rounded-md">
            {error}
          </div>
        )}

        <form onSubmit={handleAddLink} className="flex gap-3">
        <input
          type="url"
          value={newLink}
          onChange={(e) => setNewLink(e.target.value)}
          placeholder="Enter article URL"
          className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
        />
        <button
          type="submit"
          disabled={saving}
          className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Plus size={20} />
          Add Link
        </button>
        </form>
      </div>

      <div className="flex-1 mt-6 bg-gray-800 rounded-lg overflow-hidden">
        {loading ? (
          <div className="h-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
          </div>
        ) : links.length > 0 ? (
          <div className="h-full overflow-y-auto divide-y divide-gray-700">
            {links.map((link, index) => (
              <div key={index} className="flex items-center justify-between p-4 hover:bg-gray-700/50">
                <a
                  href={link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-400 hover:text-blue-300 flex items-center gap-2 truncate"
                >
                  <span className="truncate">{link}</span>
                  <ExternalLink size={16} />
                </a>
                <button
                  onClick={() => handleDeleteLink(link)}
                  className="text-red-400 hover:text-red-300 ml-4"
                  title="Delete"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            ))}
          </div>
        ) : (
          <div className="h-full flex items-center justify-center text-gray-400">
            No article links found for this date
          </div>
        )}
      </div>
    </div>
  );
};

export default ArticleLinksManager;