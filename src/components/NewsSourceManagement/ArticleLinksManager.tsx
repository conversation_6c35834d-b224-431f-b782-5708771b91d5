import React, { useState, useEffect, useRef } from 'react';
import { ref, get, set, remove } from 'firebase/database';
import { database, functions } from '../../firebaseConfig'; 
import { Calendar, Plus, Trash2, ExternalLink, RefreshCw } from 'lucide-react';
import { httpsCallable } from 'firebase/functions';
import { useChannels } from '../../context/ChannelContext';

const ArticleLinksManager = () => {
  const { channels, loading: channelsLoading } = useChannels();
  const [selectedChannel, setSelectedChannel] = useState(() => {
    const saved = localStorage.getItem('selectedNewsChannel');
    return saved || (channels.length > 0 ? channels[0].id : '');
  });
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [links, setLinks] = useState<string[]>([]);
  const [newLink, setNewLink] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [fetching, setFetching] = useState(false);

  const fetchAndStoreNewsLinks = httpsCallable(functions, 'callable_fetch_and_store_news_links');

  const handleFetchLinks = async () => {
    if (!selectedChannel) {
      setError('Please select a channel first');
      return;
    } 

    setFetching(true);
    setError(null);

    try {
      console.log("Attempting to call Firebase function with channel:", selectedChannel);
      try {
        const result = await fetchAndStoreNewsLinks({ 
          channel: selectedChannel 
        });

        if (!result?.data || typeof result.data !== 'object') {
          throw new Error('Invalid response format from server');
        }

        console.log("Function result:", result);

        // Firebase Cloud Functions return data in result.data
        const response = result.data as { 
          success?: boolean; 
          error?: string; 
          message?: string 
        };

        if (!response) {
          throw new Error('No response received from server');
        }

        if (response.error) {
          throw new Error(response.error);
        }

        if (!response.success) {
          let errorMessage = response.message;
          // Provide a more user-friendly message for internal errors
          if (errorMessage === 'internal') {
            errorMessage = 'An unexpected server error occurred. Please try again later.';
          }
          
          throw new Error(errorMessage || 'Operation failed');
        }

        await fetchLinks();
      } catch (functionError: any) {
        console.error("Firebase function error:", functionError);

        let errorMessage: string;

        // Check if it's a network error (Firebase emulator not running)
        if (functionError.code === 'functions/failed-precondition' || 
            functionError.message?.includes('network error') ||
            functionError.message?.includes('ERR_FAILED') ||
            (functionError.code === 'functions/internal' && functionError.message === 'internal')) {
          errorMessage = 'Unable to connect to Firebase Functions. Please ensure the Firebase emulator is running.';
        } else {
          // Handle other Firebase Functions specific errors
          switch(functionError.code) {
            case 'functions/unauthenticated':
              errorMessage = 'Your session has expired. Please refresh the page and try again.';
              break;
            case 'functions/internal':
              errorMessage = 'An internal server error occurred. Please try again later.';
              break;
            default:
              errorMessage = functionError?.message || 
                functionError?.details?.message || 
                'An unexpected error occurred while fetching news links.';
          }
        }
        
        throw new Error(`Firebase Function Error: ${errorMessage}`);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch news links';
      setError(errorMessage);
      console.error(err);
    } finally {
      setFetching(false);
    }
  };

  useEffect(() => {
    if (selectedChannel && selectedDate) {
      fetchLinks();
    }
  }, [selectedChannel, selectedDate]);

  useEffect(() => {
    if (selectedChannel) {
      localStorage.setItem('selectedNewsChannel', selectedChannel);
    }
  }, [selectedChannel]);

  useEffect(() => {
    if (!selectedChannel && channels.length > 0) {
      setSelectedChannel(channels[0].id);
    }
  }, [channels]);

  const fetchLinks = async () => {
    if (!selectedChannel || !selectedDate) return;

    setLoading(true);
    setError(null);

    try {
      const [year, month, day] = selectedDate.split('-');
      const linksRef = ref(database, `${selectedChannel}/article_links/${year}/${month}/${day}`);
      const snapshot = await get(linksRef);
      setLinks(snapshot.val() || []);
    } catch (err) {
      setError('Failed to fetch article links');
      console.error(err);
    }

    setLoading(false);
  };

  const validateUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const handleAddLink = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newLink.trim() || !validateUrl(newLink)) {
      setError('Please enter a valid URL');
      return;
    }

    setSaving(true);
    try {
      const [year, month, day] = selectedDate.split('-');
      const linksRef = ref(database, `${selectedChannel}/article_links/${year}/${month}/${day}`);
      
      // Add new link to the list, ensuring no duplicates
      const updatedLinks = [...new Set([...links, newLink])];
      await set(linksRef, updatedLinks);
      
      setLinks(updatedLinks);
      setNewLink('');
      setError(null);
    } catch (err) {
      setError('Failed to add link');
      console.error(err);
    }
    setSaving(false);
  };

  const handleDeleteLink = async (linkToDelete: string) => {
    if (!window.confirm('Are you sure you want to delete this link?')) return;

    setLoading(true);
    try {
      const [year, month, day] = selectedDate.split('-');
      const linksRef = ref(database, `${selectedChannel}/article_links/${year}/${month}/${day}`);
      
      const updatedLinks = links.filter(link => link !== linkToDelete);
      setLinks(updatedLinks);
      
      await set(linksRef, updatedLinks.length ? updatedLinks : null);
      
      setError(null);
    } catch (err) {
      setError('Failed to delete link');
      console.error(err);
    }
    setLoading(false);
  };

  return (
    <div className="flex flex-col h-[calc(100vh-theme(spacing.14)-theme(spacing.8))] md:h-[calc(100vh-theme(spacing.12))]">
      <div className="flex-none space-y-6">
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl font-bold text-white">Article Links</h1>
            <span className="px-2 py-1 text-sm bg-gray-700 text-gray-300 rounded-md">
              {links.length} {links.length === 1 ? 'link' : 'links'}
            </span>
            <button
              onClick={handleFetchLinks}
              disabled={fetching || channelsLoading || !selectedChannel}
              className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <RefreshCw size={20} className={fetching ? 'animate-spin' : ''} />
              {fetching ? 'Fetching...' : 'Fetch Links'}
            </button>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div>
          <label className="text-gray-300">Select Channel:</label>
          <select
            value={selectedChannel}
            onChange={(e) => setSelectedChannel(e.target.value)}
            className="ml-2 bg-gray-700 text-white px-3 py-2 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            {channels.map((channel) => (
              <option key={channel.id} value={channel.id}>
                {channel.name} ({channel.id})
              </option>
            ))}
          </select>
        </div>
        <div className="relative flex-1">
          <input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="w-full bg-gray-700 text-white px-3 py-2 pl-3 pr-10 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
          <div className="pointer-events-none absolute right-3 top-2.5 text-gray-400">
            <Calendar size={20} />
          </div>
        </div>
      </div>

        {error && (
          <div className="bg-red-500/10 border border-red-500 text-red-500 p-4 rounded-md">
            {error}
          </div>
        )}

        <form onSubmit={handleAddLink} className="flex gap-3">
        <input
          type="url"
          value={newLink}
          onChange={(e) => setNewLink(e.target.value)}
          placeholder="Enter article URL"
          className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
        />
        <button
          type="submit"
          disabled={saving}
          className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Plus size={20} />
          Add Link
        </button>
        </form>
      </div>

      <div className="flex-1 mt-6 bg-gray-800 rounded-lg overflow-hidden">
        {loading ? (
          <div className="h-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
          </div>
        ) : links.length > 0 ? (
          <div className="h-full overflow-y-auto divide-y divide-gray-700">
            {links.map((link, index) => (
              <div key={index} className="flex items-center justify-between p-4 hover:bg-gray-700/50">
                <a
                  href={link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-400 hover:text-blue-300 flex items-center gap-2 truncate"
                >
                  <span className="truncate">{link}</span>
                  <ExternalLink size={16} />
                </a>
                <button
                  onClick={() => handleDeleteLink(link)}
                  className="text-red-400 hover:text-red-300 ml-4"
                  title="Delete"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            ))}
          </div>
        ) : (
          <div className="h-full flex items-center justify-center text-gray-400">
            No article links found for this date
          </div>
        )}
      </div>
    </div>
  );
};

export default ArticleLinksManager;