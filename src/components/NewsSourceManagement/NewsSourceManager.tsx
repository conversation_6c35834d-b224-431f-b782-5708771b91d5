import React, { useState, useEffect } from 'react';
import { ref, get, push, update, remove } from 'firebase/database';
import { database, auth } from '../../firebaseConfig';
import { Plus, Edit2, Trash2, X, ExternalLink } from 'lucide-react';
import { onAuthStateChanged } from 'firebase/auth';
import { useChannels } from '../../context/ChannelContext';

interface NewsSource {
  source_name: string;
  news_page: string;
  rss_feed: string;
}

const initialFormState = {
  source_name: '',
  news_page: '',
  rss_feed: '',
};

const NewsSourceManager = () => {
  const { channels } = useChannels();
  const [selectedChannel, setSelectedChannel] = useState('');
  const [sources, setSources] = useState<Record<string, NewsSource>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState<NewsSource>(initialFormState);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setIsAuthenticated(!!user);
      if (user) {
        if (channels.length > 0) {
          setSelectedChannel(channels[0].id);
        }
      } else {
        setError('Please sign in to access news sources');
        setSources({});
      }
    });

    return () => unsubscribe();
  }, [channels]);

  const fetchSources = async () => {
    if (!isAuthenticated || !selectedChannel) {
      return;
    }

    setLoading(true);
    try {
      const sourcesRef = ref(database, `${selectedChannel}/news_sources`);
      const snapshot = await get(sourcesRef);
      setSources(snapshot.val() || {});
      setError('');
    } catch (err) {
      setError('Failed to fetch news sources');
      console.error(err);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (isAuthenticated && selectedChannel) {
      fetchSources();
    }
  }, [selectedChannel, isAuthenticated]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const validateForm = () => {
    if (!formData.source_name.trim()) {
      setError('Source name is required');
      return false;
    }
    if (!formData.news_page.trim()) {
      setError('News page URL is required');
      return false;
    }
    try {
      new URL(formData.news_page);
      if (formData.rss_feed) {
        new URL(formData.rss_feed);
      }
    } catch {
      setError('Please enter valid URLs');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setSaving(true);
    try {
      if (editingId) {
        // Update existing source
        const sourceRef = ref(database, `${selectedChannel}/news_sources/${editingId}`);
        await update(sourceRef, formData);
      } else {
        // Create new source
        const sourcesRef = ref(database, `${selectedChannel}/news_sources`);
        await push(sourcesRef, formData);
      }
      
      setShowForm(false);
      setFormData(initialFormState);
      setEditingId(null);
      await fetchSources();
      setError('');
    } catch (err) {
      setError('Failed to save news source');
      console.error(err);
    }
    setSaving(false);
  };

  const handleEdit = (id: string, source: NewsSource) => {
    setFormData(source);
    setEditingId(id);
    setShowForm(true);
    setError('');
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this source?')) return;

    try {
      const sourceRef = ref(database, `${selectedChannel}/news_sources/${id}`);
      await remove(sourceRef);
      await fetchSources();
      setError('');
    } catch (err) {
      setError('Failed to delete news source');
      console.error(err);
    }
  };

  const resetForm = () => {
    setFormData(initialFormState);
    setEditingId(null);
    setShowForm(false);
    setError('');
  };

  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="bg-red-500/10 border border-red-500 text-red-500 p-4 rounded-md">
          Please sign in to access news sources
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl font-bold text-white">News Source Management</h1>
        {!showForm && (
          <button 
            onClick={() => setShowForm(true)}
            className="w-full sm:w-auto flex items-center justify-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
          >
            <Plus size={20} />
            Add Source
          </button>
        )}
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <label className="text-gray-300">Select Channel:</label>
        <select
          value={selectedChannel}
          onChange={(e) => setSelectedChannel(e.target.value)}
          className="w-full sm:w-auto bg-gray-700 text-white px-3 py-2 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500"
        >
          {channels.map((channel) => (
            <option key={channel.id} value={channel.id}>
              {channel.name} ({channel.id})
            </option>
          ))}
        </select>
      </div>

      {error && (
        <div className="bg-red-500/10 border border-red-500 text-red-500 p-4 rounded-md">
          {error}
        </div>
      )}

      {showForm && (
        <div className="bg-gray-800 p-4 sm:p-6 rounded-lg border border-gray-700">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-white">
              {editingId ? 'Edit Source' : 'Add New Source'}
            </h2>
            <button 
              onClick={resetForm}
              className="text-gray-400 hover:text-white"
            >
              <X size={20} />
            </button>
          </div>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Source Name
              </label>
              <input
                type="text"
                name="source_name"
                value={formData.source_name}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="Enter source name"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                News Page URL
              </label>
              <input
                type="url"
                name="news_page"
                value={formData.news_page}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="https://example.com"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                RSS Feed URL (Optional)
              </label>
              <input
                type="url"
                name="rss_feed"
                value={formData.rss_feed}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="https://example.com/feed"
              />
            </div>
            <div className="flex flex-col sm:flex-row justify-end gap-3">
              <button
                type="button"
                onClick={resetForm}
                className="w-full sm:w-auto px-4 py-2 text-gray-300 hover:text-white transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={saving}
                className="w-full sm:w-auto px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? 'Saving...' : editingId ? 'Update Source' : 'Add Source'}
              </button>
            </div>
          </form>
        </div>
      )}

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
        </div>
      ) : (
        <>
          <div className="bg-gray-800 rounded-lg overflow-x-auto">
          <div className="min-w-full">
            <table className="w-full">
              <thead>
                <tr className="bg-gray-700">
                  <th className="px-4 sm:px-6 py-3 text-left text-sm font-medium text-gray-300">Source Name</th>
                  <th className="px-4 sm:px-6 py-3 text-left text-sm font-medium text-gray-300 hidden md:table-cell">News Page</th>
                  <th className="px-4 sm:px-6 py-3 text-left text-sm font-medium text-gray-300 hidden lg:table-cell">RSS Feed</th>
                  <th className="px-4 sm:px-6 py-3 text-right text-sm font-medium text-gray-300">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700">
                {Object.entries(sources).map(([id, source]) => (
                  <tr key={id} className="hover:bg-gray-700/50">
                    <td className="px-4 sm:px-6 py-4 text-sm text-white">
                      <div className="flex flex-col md:hidden gap-1">
                        <span>{source.source_name}</span>
                        <a 
                          href={source.news_page} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-400 hover:text-blue-300 text-xs flex items-center gap-1"
                        >
                          Visit Site <ExternalLink size={12} />
                        </a>
                      </div>
                      <span className="hidden md:block">{source.source_name}</span>
                    </td>
                    <td className="px-4 sm:px-6 py-4 text-sm text-white hidden md:table-cell">
                      <a 
                        href={source.news_page} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-400 hover:text-blue-300 flex items-center gap-1"
                      >
                        <span className="truncate max-w-[200px]">{source.news_page}</span>
                        <ExternalLink size={14} />
                      </a>
                    </td>
                    <td className="px-4 sm:px-6 py-4 text-sm text-white hidden lg:table-cell">
                      {source.rss_feed && (
                        <a 
                          href={source.rss_feed} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-400 hover:text-blue-300 flex items-center gap-1"
                        >
                          <span className="truncate max-w-[200px]">{source.rss_feed}</span>
                          <ExternalLink size={14} />
                        </a>
                      )}
                    </td>
                    <td className="px-4 sm:px-6 py-4 text-sm text-right space-x-2">
                      <button 
                        onClick={() => handleEdit(id, source)}
                        className="text-blue-400 hover:text-blue-300"
                        title="Edit"
                      >
                        <Edit2 size={16} />
                      </button>
                      <button 
                        onClick={() => handleDelete(id)}
                        className="text-red-400 hover:text-red-300"
                        title="Delete"
                      >
                        <Trash2 size={16} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          </div>
        </>
      )}
    </div>
  );
};

export default NewsSourceManager;