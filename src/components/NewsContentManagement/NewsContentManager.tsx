import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  collection, 
  query, 
  where, 
  orderBy, 
  limit, 
  getDocs, 
  doc, 
  getDoc, 
  setDoc,
  updateDoc,
  deleteDoc,
  Timestamp,
  startAfter,
  serverTimestamp
} from 'firebase/firestore';
import { firestore, functions } from '../../firebaseConfig';
import { useChannels } from '../../context/ChannelContext';
import { format } from 'date-fns';
import { Calendar, ExternalLink, X, Plus, Edit2, Trash2, Download } from 'lucide-react';
import { httpsCallable } from 'firebase/functions';

interface NewsContent {
  title: string;
  content: string;
  url?: string;
  source?: string;
  publish_date: string;
  publish_timestamp: Timestamp;
  extraction_timestamp: Timestamp;
  author?: string;
  images?: string[];
  top_image?: string;
  doc_id: string;
  channel: string;
}

interface NewsContentFormData {
  title: string;
  content: string;
  url: string;
  top_image: string;
  author: string;
  publish_date: string;
}

const initialFormData: NewsContentFormData = {
  title: '',
  content: '',
  url: '',
  top_image: '',
  author: '',
  publish_date: new Date().toISOString().slice(0, 16), // Format: YYYY-MM-DDTHH:mm
};

const PAGE_SIZE = 15;

const NewsContentManager = () => {
  const { channels } = useChannels();
  const [selectedChannel, setSelectedChannel] = useState(() => {
    const saved = localStorage.getItem('selectedNewsChannel');
    return saved || (channels.length > 0 ? channels[0].id : '');
  });
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [articles, setArticles] = useState<NewsContent[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedArticle, setSelectedArticle] = useState<NewsContent | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [lastDoc, setLastDoc] = useState<any>(null);
  const observer = useRef<IntersectionObserver>();
  const loadingRef = useRef<HTMLDivElement>(null);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState<NewsContentFormData>(initialFormData);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);

  // Extract Articles functionality
  const [extracting, setExtracting] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Define the callable function
  const extractArticleContent = httpsCallable(
    functions,
    'callable_extract_article_content',
    { timeout: 540000 } // 9 minute timeout (540 seconds)
  );

  const handleExtractContent = async () => {
    if (!selectedChannel) {
      setError('Please select a channel first');
      return;
    }

    setExtracting(true);
    setError(null);
    setSuccessMessage(null);

    try {
      console.log("Calling article extraction function with data:", {
        channel: selectedChannel,
      });

      const result = await extractArticleContent({
        channel: selectedChannel,
      });

      console.log("Function result:", result);

      // Firebase Cloud Functions return data in result.data
      const response = result.data as any;

      if (!response) {
        throw new Error('No response received from server');
      }

      if (response.error) {
        throw new Error(response.error);
      }

      if (response.status === "error") {
        throw new Error(response.message || 'Operation failed');
      }

      setSuccessMessage(`Successfully extracted ${response.extracted_success || 'some'} articles`);

      // Refresh article list
      fetchArticles();
    } catch (err: any) {
      console.error("Function error details:", err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to extract article content';
      setError(errorMessage);
    } finally {
      setExtracting(false);
    }
  };

  useEffect(() => {
    if (selectedChannel) {
      localStorage.setItem('selectedNewsChannel', selectedChannel);
    }
  }, [selectedChannel]);

  useEffect(() => {
    if (!selectedChannel && channels.length > 0) {
      setSelectedChannel(channels[0].id);
    }
  }, [channels]);

  const fetchArticles = async (isLoadMore = false) => {
    if (!selectedChannel || loading) return;

    setLoading(true);
    setError(null);

    try {
      const articlesRef = collection(firestore, `channels/${selectedChannel}/news_contents`);
      let q = query(articlesRef, orderBy('publish_timestamp', 'desc'), limit(PAGE_SIZE));

      if (startDate) {
        const startDateTime = new Date(startDate);
        startDateTime.setHours(0, 0, 0, 0);
        q = query(q, where('publish_timestamp', '>=', startDateTime));
      }

      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        q = query(q, where('publish_timestamp', '<=', endDateTime));
      }

      if (isLoadMore && lastDoc) {
        q = query(q, startAfter(lastDoc));
      }

      const snapshot = await getDocs(q);
      const fetchedArticles = snapshot.docs.map(doc => ({
        ...doc.data(),
        doc_id: doc.id,
      })) as NewsContent[];

      setLastDoc(snapshot.docs[snapshot.docs.length - 1]);
      setHasMore(snapshot.docs.length === PAGE_SIZE);
      
      if (isLoadMore) {
        setArticles(prev => [...prev, ...fetchedArticles]);
      } else {
        setArticles(fetchedArticles);
      }
    } catch (err) {
      setError('Failed to fetch articles');
      console.error(err);
    }

    setLoading(false);
  };

  useEffect(() => {
    setArticles([]);
    setLastDoc(null);
    setHasMore(true);
    setError(null);
    setSuccessMessage(null);
    fetchArticles();
  }, [selectedChannel, startDate, endDate]);

  const lastArticleRef = useCallback((node: HTMLTableRowElement) => {
    if (loading) return;

    if (observer.current) observer.current.disconnect();
    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        fetchArticles(true);
      }
    });

    if (node) observer.current.observe(node);
  }, [loading, hasMore]);

  const handleViewArticle = async (article: NewsContent) => {
    try {
      const articleRef = doc(firestore, `channels/${selectedChannel}/news_contents/${article.doc_id}`);
      const articleDoc = await getDoc(articleRef);
      
      if (articleDoc.exists()) {
        setSelectedArticle({
          ...articleDoc.data(),
          doc_id: articleDoc.id,
        } as NewsContent);
      }
    } catch (err) {
      console.error('Error fetching article details:', err);
    }
  };

  const handleEdit = (article: NewsContent) => {
    setFormData({
      title: article.title,
      content: article.content,
      url: article.url || '',
      top_image: article.top_image || '',
      author: article.author || '',
      publish_date: article.publish_timestamp.toDate().toISOString().slice(0, 16),
    });
    setEditingId(article.doc_id);
    setShowForm(true);
  };

  const handleDelete = async (docId: string) => {
    if (!window.confirm('Are you sure you want to delete this article?')) return;

    try {
      const articleRef = doc(firestore, `channels/${selectedChannel}/news_contents/${docId}`);
      await deleteDoc(articleRef);
      setArticles(prev => prev.filter(article => article.doc_id !== docId));
      setError(null);
    } catch (err) {
      setError('Failed to delete article');
      console.error(err);
    }
  };

  const validateForm = () => {
    if (!formData.title.trim() || !formData.content.trim()) {
      setError('Title and content are required');
      return false;
    }
    if (formData.url && !isValidUrl(formData.url)) {
      setError('Please enter a valid URL');
      return false;
    }
    if (formData.top_image && !isValidUrl(formData.top_image)) {
      setError('Please enter a valid image URL');
      return false;
    }
    return true;
  };

  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setSaving(true);
    try {
      // Create a precise timestamp string with microseconds
      const publishDate = new Date(formData.publish_date);
      const microseconds = publishDate.getMilliseconds() * 1000; // Convert milliseconds to microseconds
      const publishDateString = `${publishDate.toISOString().slice(0, 23)}${microseconds.toString().padStart(3, '0')}`;

      // Create Firestore timestamp objects
      const publishTimestamp = Timestamp.fromDate(publishDate);
      const extractionTimestamp = Timestamp.fromDate(new Date());

      const articleData = {
        title: formData.title,
        content: formData.content,
        url: formData.url || null,
        top_image: formData.top_image || null,
        author: formData.author || null,
        publish_date: publishDateString,
        publish_timestamp: publishTimestamp,
        extraction_timestamp: extractionTimestamp,
        channel: selectedChannel,
      };

      if (editingId) {
        const articleRef = doc(firestore, `channels/${selectedChannel}/news_contents/${editingId}`);
        await updateDoc(articleRef, {
          ...articleData,
          doc_id: editingId,
        });
      } else {
        const articlesRef = collection(firestore, `channels/${selectedChannel}/news_contents`);
        const newDocRef = doc(articlesRef);
        await setDoc(newDocRef, {
          ...articleData,
          doc_id: newDocRef.id,
        });
      }

      resetForm();
      fetchArticles();
      setError(null);
    } catch (err) {
      setError('Failed to save article');
      console.error(err);
    }
    setSaving(false);
  };

  const resetForm = () => {
    setFormData(initialFormData);
    setEditingId(null);
    setShowForm(false);
    setError(null);
    setSuccessMessage(null);
  };

  const formatDate = (timestamp: Timestamp) => {
    try {
      return format(timestamp.toDate(), 'MMM d, yyyy h:mm a');
    } catch (err) {
      console.error('Date formatting error:', err);
      return 'Invalid date';
    }
  };

  return (
    <div className="flex flex-col h-[calc(100vh-theme(spacing.14)-theme(spacing.8))] md:h-[calc(100vh-theme(spacing.12))]">
      <div className="flex-none">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">News Content Management</h1>
          {!showForm && (
            <div className="flex gap-3">
              <button
                onClick={handleExtractContent}
                disabled={extracting || !selectedChannel}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Extract article content from article links"
              >
                <Download size={20} className={extracting ? 'animate-spin' : ''} />
                {extracting ? 'Extracting...' : 'Extract Articles'}
              </button>
              <button
                onClick={() => setShowForm(true)}
                className="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
              >
                <Plus size={20} />
                Add Article
              </button>
            </div>
          )}
        </div>

        {showForm && (
          <div className="bg-gray-800 p-6 rounded-lg border border-gray-700 mb-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-white">
                {editingId ? `Edit Article (ID: ${editingId})` : 'Add New Article'}
              </h2>
              <button
                onClick={resetForm}
                className="text-gray-400 hover:text-white"
              >
                <X size={20} />
              </button>
            </div>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Title
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  placeholder="Article title"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Content
                </label>
                <textarea
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  className="w-full h-32 px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  placeholder="Article content"
                  required
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    URL
                  </label>
                  <input
                    type="url"
                    value={formData.url}
                    onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    placeholder="https://example.com/article"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Image URL
                  </label>
                  <input
                    type="url"
                    value={formData.top_image}
                    onChange={(e) => setFormData(prev => ({ ...prev, top_image: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Author
                  </label>
                  <input
                    type="text"
                    value={formData.author}
                    onChange={(e) => setFormData(prev => ({ ...prev, author: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    placeholder="Author name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Publish Date & Time
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.publish_date}
                    onChange={(e) => setFormData(prev => ({ ...prev, publish_date: e.target.value }))}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    required
                  />
                </div>
              </div>
              <div className="flex justify-end gap-3">
                <button
                  type="button"
                  onClick={resetForm}
                  className="px-4 py-2 text-gray-300 hover:text-white transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={saving}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {saving ? 'Saving...' : editingId ? 'Update Article' : 'Add Article'}
                </button>
              </div>
            </form>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Channel
            </label>
            <select
              value={selectedChannel}
              onChange={(e) => setSelectedChannel(e.target.value)}
              className="w-full bg-gray-700 text-white px-3 py-2 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              {channels.map((channel) => (
                <option key={channel.id} value={channel.id}>
                  {channel.name} ({channel.id})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Start Date
            </label>
            <div className="relative">
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="w-full bg-gray-700 text-white px-3 py-2 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
              <Calendar className="absolute right-3 top-2.5 text-gray-400" size={20} />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              End Date
            </label>
            <div className="relative">
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="w-full bg-gray-700 text-white px-3 py-2 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
              <Calendar className="absolute right-3 top-2.5 text-gray-400" size={20} />
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-500/10 border border-red-500 text-red-500 p-4 rounded-md mb-6">
            {error}
          </div>
        )}

        {successMessage && (
          <div className="bg-green-500/10 border border-green-500 text-green-500 p-4 rounded-md mb-6">
            {successMessage}
          </div>
        )}
      </div>

      <div className="flex-1 bg-gray-800 rounded-lg overflow-hidden">
        <div className="h-full flex flex-col">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="w-3/5 px-6 py-3 text-left text-sm font-medium text-gray-300">Title</th>
                <th className="w-1/5 px-6 py-3 text-left text-sm font-medium text-gray-300">Published</th>
                <th className="w-1/5 px-6 py-3 text-right text-sm font-medium text-gray-300">Actions</th>
              </tr>
            </thead>
          </table>
          
          <div className="flex-1 overflow-y-auto">
            <table className="w-full">
              <tbody className="divide-y divide-gray-700">
                {articles.map((article, index) => (
                  <tr 
                    key={article.doc_id} 
                    ref={index === articles.length - 1 ? lastArticleRef : null}
                    className="hover:bg-gray-700/50"
                  >
                    <td className="w-3/5 px-6 py-4 text-sm text-white">
                      <div className="line-clamp-2">{article.title}</div>
                    </td>
                    <td className="w-1/5 px-6 py-4 text-sm text-white">
                      {formatDate(article.publish_timestamp)}
                    </td>
                    <td className="w-1/5 px-6 py-4 text-sm text-right space-x-3">
                      <button
                        onClick={() => handleViewArticle(article)}
                        className="text-blue-400 hover:text-blue-300"
                        title="View"
                      >
                        <ExternalLink size={16} />
                      </button>
                      <button
                        onClick={() => handleEdit(article)}
                        className="text-blue-400 hover:text-blue-300"
                        title="Edit"
                      >
                        <Edit2 size={16} />
                      </button>
                      <button
                        onClick={() => handleDelete(article.doc_id)}
                        className="text-red-400 hover:text-red-300"
                        title="Delete"
                      >
                        <Trash2 size={16} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {loading && (
              <div ref={loadingRef} className="text-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
              </div>
            )}
          </div>
        </div>
      </div>

      {selectedArticle && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b border-gray-700">
              <h2 className="text-xl font-semibold text-white">Article Details</h2>
              <button
                onClick={() => setSelectedArticle(null)}
                className="text-gray-400 hover:text-white"
              >
                <X size={20} />
              </button>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-8rem)]">
              <div className="space-y-6">
                <div>
                  <h3 className="text-2xl font-bold text-white mb-2">{selectedArticle.title}</h3>
                  <div className="flex flex-wrap gap-4 text-sm text-gray-400">
                    {selectedArticle.source && <span>Source: {selectedArticle.source}</span>}
                    {selectedArticle.author && <span>Author: {selectedArticle.author}</span>}
                    <span>Published: {formatDate(selectedArticle.publish_timestamp)}</span>
                    <span>Document ID: {selectedArticle.doc_id}</span>
                  </div>
                </div>

                {selectedArticle.top_image && (
                  <img
                    src={selectedArticle.top_image}
                    alt="Article header"
                    className="w-full h-64 object-cover rounded-lg"
                  />
                )}

                <div className="prose prose-invert max-w-none">
                  <p className="whitespace-pre-wrap">{selectedArticle.content}</p>
                </div>

                {selectedArticle.images && selectedArticle.images.length > 0 && (
                  <div>
                    <h4 className="text-lg font-semibold text-white mb-3">Article Images</h4>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {selectedArticle.images.map((image, index) => (
                        <img
                          key={index}
                          src={image}
                          alt={`Article image ${index + 1}`}
                          className="w-full h-40 object-cover rounded-lg"
                        />
                      ))}
                    </div>
                  </div>
                )}

                {selectedArticle.url && (
                  <div className="flex justify-end">
                    <a
                      href={selectedArticle.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 text-blue-400 hover:text-blue-300"
                    >
                      View Original Article
                      <ExternalLink size={16} />
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NewsContentManager;