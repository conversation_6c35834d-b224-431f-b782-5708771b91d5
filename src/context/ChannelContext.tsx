import React, { createContext, useContext, useEffect, useState } from 'react';
import { collection, getDocs } from 'firebase/firestore';
import { firestore } from '../firebaseConfig';

interface Channel {
  id: string;
  name: string;
  type: string;
}

interface ChannelContextType {
  channels: Channel[];
  loading: boolean;
  error: string | null;
  refreshChannels: () => Promise<void>;
}

const ChannelContext = createContext<ChannelContextType>({
  channels: [],
  loading: true,
  error: null,
  refreshChannels: async () => {},
});

export const ChannelProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [channels, setChannels] = useState<Channel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchChannels = async () => {
    setLoading(true);
    try {
      const channelsRef = collection(firestore, 'channels');
      const snapshot = await getDocs(channelsRef);
      const channelList = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      } as Channel));
      setChannels(channelList);
      setError(null);
    } catch (err) {
      setError('Failed to fetch channels');
      console.error(err);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchChannels();
  }, []);

  return (
    <ChannelContext.Provider value={{ channels, loading, error, refreshChannels: fetchChannels }}>
      {children}
    </ChannelContext.Provider>
  );
};

export const useChannels = () => useContext(ChannelContext);