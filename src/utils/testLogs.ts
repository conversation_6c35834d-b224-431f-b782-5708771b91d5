import { ref, push, set } from 'firebase/database';
import { database } from '../firebaseConfig';

// Utility function to generate test logs
export const generateTestLogs = async () => {
  const logsRef = ref(database, 'logs');
  
  const sampleLogs = [
    {
      severity: 'INFO',
      message: 'User successfully logged in',
      timestamp: Date.now(),
      log_id: 'auth-001',
      data: {
        user_id: 'user123',
        ip_address: '***********',
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    },
    {
      severity: 'ERROR',
      message: 'Failed to fetch news articles from RSS feed',
      timestamp: Date.now() - 300000, // 5 minutes ago
      log_id: 'rss-error-001',
      data: {
        feed_url: 'https://example.com/rss',
        error_code: 'TIMEOUT',
        retry_count: 3,
        channel: 'UK'
      }
    },
    {
      severity: 'WARNING',
      message: 'High memory usage detected',
      timestamp: Date.now() - 600000, // 10 minutes ago
      log_id: 'system-warn-001',
      data: {
        memory_usage: '85%',
        threshold: '80%',
        process_id: 'news-scraper-001'
      }
    },
    {
      severity: 'INFO',
      message: 'Successfully extracted 15 articles',
      timestamp: Date.now() - 900000, // 15 minutes ago
      log_id: 'extract-success-001',
      data: {
        channel: 'GH',
        articles_processed: 15,
        success_rate: '93%',
        processing_time: '2.3s'
      }
    },
    {
      severity: 'ERROR',
      message: 'Database connection failed',
      timestamp: Date.now() - 1200000, // 20 minutes ago
      log_id: 'db-error-001',
      data: {
        database: 'firebase-realtime',
        error_message: 'Connection timeout',
        retry_attempts: 5
      }
    }
  ];

  try {
    for (const log of sampleLogs) {
      // Create a time-sortable key
      const key = `${log.timestamp}-${Math.random().toString(36).substr(2, 8)}`;
      await set(ref(database, `logs/${key}`), log);
    }
    console.log('Test logs generated successfully!');
  } catch (error) {
    console.error('Error generating test logs:', error);
  }
};

// Function to clear all logs (for testing)
export const clearAllLogs = async () => {
  try {
    await set(ref(database, 'logs'), null);
    console.log('All logs cleared!');
  } catch (error) {
    console.error('Error clearing logs:', error);
  }
};
